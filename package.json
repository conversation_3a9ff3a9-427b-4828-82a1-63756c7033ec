{"name": "vue-plugin-hiprint", "description": "hiprint for vue2.0", "version": "0.0.16", "author": "CcSimple", "license": "MIT", "main": "dist/vue-plugin-hiprint.js", "private": false, "repository": {"type": "git", "url": "https://github.com/CcSimple/vue-plugin-hiprint.git"}, "bugs": {"url": "https://github.com/CcSimple/vue-plugin-hiprint/issues"}, "keywords": ["vue", "<PERSON><PERSON><PERSON>", "print"], "files": ["dist"], "scripts": {"serve": "vite", "build-demo": "vite build", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^3.2.0", "concurrent-tasks": "^1.0.7", "vue": "^3.2.25", "vue-ls": "^4.0.0", "vue-plugin-hiprint": "^0.0.18"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-vue": "^2.2.0", "cjs2esmodule": "^1.1.1", "less-loader": "^6.1.1", "vite": "^2.8.0"}}